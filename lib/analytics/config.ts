import type { AnalyticsConfig } from './types'

export const ANALYTICS_CONFIG: AnalyticsConfig = {
  enabled: true,
  debug: process.env.NODE_ENV === 'development',

  providers: [
    {
      name: 'console',
      enabled: true,
      config: {
        // Console provider doesn't need configuration
      }
    },
    {
      name: 'posthog',
      enabled: !!(process.env.NEXT_PUBLIC_POSTHOG_KEY && process.env.NEXT_PUBLIC_POSTHOG_HOST),
      config: {
        apiKey: process.env.NEXT_PUBLIC_POSTHOG_KEY,
        host: process.env.NEXT_PUBLIC_POSTHOG_HOST,
      }
    }
    // Easy to add more providers here:
    // {
    //   name: 'amplitude',
    //   enabled: !!process.env.NEXT_PUBLIC_AMPLITUDE_API_KEY,
    //   config: {
    //     apiKey: process.env.NEXT_PUBLIC_AMPLITUDE_API_KEY,
    //   }
    // },
    // {
    //   name: 'meta-pixel',
    //   enabled: !!process.env.NEXT_PUBLIC_META_PIXEL_ID,
    //   config: {
    //     pixelId: process.env.NEXT_PUBLIC_META_PIXEL_ID,
    //   }
    // }
  ]
} as const
