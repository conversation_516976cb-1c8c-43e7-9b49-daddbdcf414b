#!/usr/bin/env node

/**
 * PostHog Integration Test Script
 * 
 * This script tests the PostHog integration by:
 * 1. Checking environment variables
 * 2. Verifying the PostHog provider is configured
 * 3. Testing basic functionality
 */

const fs = require('fs')
const path = require('path')

console.log('🧪 PostHog Integration Test\n')

// Test 1: Check environment variables
console.log('1️⃣ Checking environment variables...')
const envPath = path.join(process.cwd(), '.env.local')

if (!fs.existsSync(envPath)) {
  console.error('❌ .env.local file not found')
  process.exit(1)
}

const envContent = fs.readFileSync(envPath, 'utf8')
const hasPostHogKey = envContent.includes('NEXT_PUBLIC_POSTHOG_KEY=')
const hasPostHogHost = envContent.includes('NEXT_PUBLIC_POSTHOG_HOST=')

if (hasPostHogKey && hasPostHogHost) {
  console.log('✅ PostHog environment variables found')
} else {
  console.error('❌ Missing PostHog environment variables')
  if (!hasPostHogKey) console.error('   Missing: NEXT_PUBLIC_POSTHOG_KEY')
  if (!hasPostHogHost) console.error('   Missing: NEXT_PUBLIC_POSTHOG_HOST')
  process.exit(1)
}

// Test 2: Check PostHog provider file
console.log('\n2️⃣ Checking PostHog provider implementation...')
const providerPath = path.join(process.cwd(), 'lib/analytics/providers/posthog.ts')

if (!fs.existsSync(providerPath)) {
  console.error('❌ PostHog provider file not found')
  process.exit(1)
}

const providerContent = fs.readFileSync(providerPath, 'utf8')
const hasPostHogClass = providerContent.includes('export class PostHogProvider')
const hasInitialize = providerContent.includes('async initialize(')
const hasTrack = providerContent.includes('async track(')
const hasIdentify = providerContent.includes('async identify(')

if (hasPostHogClass && hasInitialize && hasTrack && hasIdentify) {
  console.log('✅ PostHog provider implementation found')
} else {
  console.error('❌ PostHog provider implementation incomplete')
  process.exit(1)
}

// Test 3: Check analytics configuration
console.log('\n3️⃣ Checking analytics configuration...')
const configPath = path.join(process.cwd(), 'lib/analytics/config.ts')

if (!fs.existsSync(configPath)) {
  console.error('❌ Analytics config file not found')
  process.exit(1)
}

const configContent = fs.readFileSync(configPath, 'utf8')
const hasPostHogConfig = configContent.includes("name: 'posthog'")

if (hasPostHogConfig) {
  console.log('✅ PostHog configured in analytics config')
} else {
  console.error('❌ PostHog not found in analytics configuration')
  process.exit(1)
}

// Test 4: Check analytics manager
console.log('\n4️⃣ Checking analytics manager...')
const managerPath = path.join(process.cwd(), 'lib/analytics/manager.ts')

if (!fs.existsSync(managerPath)) {
  console.error('❌ Analytics manager file not found')
  process.exit(1)
}

const managerContent = fs.readFileSync(managerPath, 'utf8')
const hasPostHogCase = managerContent.includes("case 'posthog':")
const hasPostHogImport = managerContent.includes('PostHogProvider')

if (hasPostHogCase && hasPostHogImport) {
  console.log('✅ PostHog provider registered in analytics manager')
} else {
  console.error('❌ PostHog provider not properly registered in analytics manager')
  process.exit(1)
}

// Test 5: Check package.json for PostHog dependency
console.log('\n5️⃣ Checking PostHog dependency...')
const packagePath = path.join(process.cwd(), 'package.json')

if (!fs.existsSync(packagePath)) {
  console.error('❌ package.json file not found')
  process.exit(1)
}

const packageContent = JSON.parse(fs.readFileSync(packagePath, 'utf8'))
const hasPostHogDep = packageContent.dependencies && packageContent.dependencies['posthog-js']

if (hasPostHogDep) {
  console.log(`✅ PostHog dependency found (version: ${packageContent.dependencies['posthog-js']})`)
} else {
  console.error('❌ PostHog dependency not found in package.json')
  process.exit(1)
}

// All tests passed
console.log('\n🎉 All PostHog integration tests passed!')
console.log('\n📋 Next steps:')
console.log('1. Visit http://localhost:3000 in your browser')
console.log('2. Open browser developer tools (F12)')
console.log('3. Check the Console tab for PostHog initialization messages')
console.log('4. Use the Analytics Test Panel on the page to test events')
console.log('5. Check your PostHog dashboard for incoming events')
console.log('\n🔍 What to look for in the browser console:')
console.log('- "[PostHog] Initializing with config: ..."')
console.log('- "[PostHog] Initialized successfully"')
console.log('- "[Analytics] Initialized provider: posthog"')
console.log('- "[Analytics] Manager initialized with 2 providers"')
console.log('\n🚀 PostHog integration is ready to test!')
